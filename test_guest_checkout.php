<?php

/**
 * Simple test script to verify guest checkout implementation
 * Run this with: php test_guest_checkout.php
 */

require_once 'vendor/autoload.php';

echo "=== Guest Checkout Implementation Test ===\n\n";

// Test 1: Check if files exist
$files = [
    'database/migrations/2025_01_15_000002_make_customer_id_nullable_for_guest_checkout.php',
    'app/Http/Requests/GuestCheckoutRequest.php',
    'app/Services/CartService.php',
    'app/Services/GuestRegistrationService.php',
    'tests/Feature/GuestCheckoutTest.php',
    'tests/Unit/CartServiceTest.php'
];

echo "1. Checking if all files exist:\n";
foreach ($files as $file) {
    if (file_exists($file)) {
        echo "   ✓ $file\n";
    } else {
        echo "   ✗ $file (MISSING)\n";
    }
}

// Test 2: Check if classes can be loaded
echo "\n2. Checking if classes can be loaded:\n";

try {
    if (class_exists('App\Http\Requests\GuestCheckoutRequest')) {
        echo "   ✓ GuestCheckoutRequest class\n";
    } else {
        echo "   ✗ GuestCheckoutRequest class (NOT FOUND)\n";
    }
} catch (Exception $e) {
    echo "   ✗ GuestCheckoutRequest class (ERROR: " . $e->getMessage() . ")\n";
}

try {
    if (class_exists('App\Services\CartService')) {
        echo "   ✓ CartService class\n";
    } else {
        echo "   ✗ CartService class (NOT FOUND)\n";
    }
} catch (Exception $e) {
    echo "   ✗ CartService class (ERROR: " . $e->getMessage() . ")\n";
}

try {
    if (class_exists('App\Services\GuestRegistrationService')) {
        echo "   ✓ GuestRegistrationService class\n";
    } else {
        echo "   ✗ GuestRegistrationService class (NOT FOUND)\n";
    }
} catch (Exception $e) {
    echo "   ✗ GuestRegistrationService class (ERROR: " . $e->getMessage() . ")\n";
}

// Test 3: Check if CheckoutCont has been updated
echo "\n3. Checking CheckoutCont updates:\n";
$checkoutContent = file_get_contents('app/Http/Controllers/CheckoutCont.php');

if (strpos($checkoutContent, 'GuestCheckoutRequest') !== false) {
    echo "   ✓ CheckoutCont uses GuestCheckoutRequest\n";
} else {
    echo "   ✗ CheckoutCont does not use GuestCheckoutRequest\n";
}

if (strpos($checkoutContent, 'GuestRegistrationService') !== false) {
    echo "   ✓ CheckoutCont uses GuestRegistrationService\n";
} else {
    echo "   ✗ CheckoutCont does not use GuestRegistrationService\n";
}

if (strpos($checkoutContent, 'customer_id.*null') !== false || strpos($checkoutContent, '\$customer_id') !== false) {
    echo "   ✓ CheckoutCont handles null customer_id\n";
} else {
    echo "   ✗ CheckoutCont may not handle null customer_id properly\n";
}

// Test 4: Check if checkout view has been updated
echo "\n4. Checking checkout view updates:\n";
$checkoutViewContent = file_get_contents('resources/views/frontend/checkout.blade.php');

if (strpos($checkoutViewContent, 'create_account') !== false) {
    echo "   ✓ Checkout view has create account option\n";
} else {
    echo "   ✗ Checkout view missing create account option\n";
}

if (strpos($checkoutViewContent, 'password_fields') !== false) {
    echo "   ✓ Checkout view has password fields\n";
} else {
    echo "   ✗ Checkout view missing password fields\n";
}

// Test 5: Check routes
echo "\n5. Checking routes:\n";
$routesContent = file_get_contents('routes/web.php');

if (strpos($routesContent, 'cart.merge') !== false) {
    echo "   ✓ Cart merge route exists\n";
} else {
    echo "   ✗ Cart merge route missing\n";
}

if (strpos($routesContent, 'cart.count') !== false) {
    echo "   ✓ Cart count route exists\n";
} else {
    echo "   ✗ Cart count route missing\n";
}

echo "\n=== Test Complete ===\n";
echo "\nNext steps:\n";
echo "1. Run: php artisan migrate\n";
echo "2. Test the checkout process manually\n";
echo "3. Run: php artisan test (if PHPUnit is configured)\n";
echo "4. Deploy to production\n";

?>
