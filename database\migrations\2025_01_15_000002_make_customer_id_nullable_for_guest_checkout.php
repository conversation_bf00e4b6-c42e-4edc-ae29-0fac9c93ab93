<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Make customer_id nullable in order_tabs table for guest orders
        Schema::table('order_tabs', function (Blueprint $table) {
            $table->integer('customer_id')->nullable()->change();
        });

        // Make customer_id nullable in ordereditems_tabs table for guest orders
        Schema::table('ordereditems_tabs', function (Blueprint $table) {
            $table->integer('customer_id')->nullable()->change();
        });

        // Make customer_id nullable in billing_tabs table for guest orders
        Schema::table('billing_tabs', function (Blueprint $table) {
            $table->integer('customer_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert customer_id to NOT NULL (only if no guest orders exist)
        Schema::table('order_tabs', function (Blueprint $table) {
            $table->integer('customer_id')->nullable(false)->change();
        });

        Schema::table('ordereditems_tabs', function (Blueprint $table) {
            $table->integer('customer_id')->nullable(false)->change();
        });

        Schema::table('billing_tabs', function (Blueprint $table) {
            $table->integer('customer_id')->nullable(false)->change();
        });
    }
};
