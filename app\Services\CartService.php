<?php

namespace App\Services;

use App\Models\cartMod;
use App\Models\Product_list;
use App\Models\Color;
use App\Models\Size;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;

class CartService
{
    /**
     * Get cart items for current user (guest or authenticated)
     *
     * @return \Illuminate\Support\Collection
     */
    public function getCartItems()
    {
        if (Auth::guard('cust_login')->check()) {
            return cartMod::where('customer_id', Auth::guard('cust_login')->id())->get();
        }

        return $this->getGuestCartItems();
    }

    /**
     * Get guest cart items from session
     *
     * @return \Illuminate\Support\Collection
     */
    public function getGuestCartItems()
    {
        $guestCart = session()->get('guest_cart', []);
        $cartItems = collect();

        foreach ($guestCart as $item) {
            $cartItem = (object) [
                'id' => $item['id'],
                'product_id' => $item['product_id'],
                'color_id' => $item['color_id'],
                'size_id' => $item['size_id'],
                'quantity' => $item['quantity'],
                'item_type' => $item['item_type'] ?? null,
                'customer_picture' => $item['customer_picture'] ?? null,
                'created_at' => $item['created_at'] ?? now(),
            ];

            // Load relationships
            $cartItem->relto_product = Product_list::find($item['product_id']);
            $cartItem->relto_color = Color::find($item['color_id']);
            $cartItem->relto_size = Size::find($item['size_id']);

            $cartItems->push($cartItem);
        }

        return $cartItems;
    }

    /**
     * Add item to cart (guest or authenticated user)
     *
     * @param array $data
     * @return array
     */
    public function addToCart($data)
    {
        if (Auth::guard('cust_login')->check()) {
            return $this->addToUserCart($data);
        }

        return $this->addToGuestCart($data);
    }

    /**
     * Add item to authenticated user's cart
     *
     * @param array $data
     * @return array
     */
    private function addToUserCart($data)
    {
        $existingItem = cartMod::where('customer_id', Auth::guard('cust_login')->id())
            ->where('product_id', $data['product_id'])
            ->where('color_id', $data['color_id'])
            ->where('size_id', $data['size_id'])
            ->where('item_type', $data['item_type'] ?? null)
            ->first();

        if ($existingItem) {
            $existingItem->increment('quantity', $data['quantity']);
            $message = 'Item quantity updated in cart!';
        } else {
            cartMod::create([
                'customer_id' => Auth::guard('cust_login')->id(),
                'product_id' => $data['product_id'],
                'color_id' => $data['color_id'],
                'size_id' => $data['size_id'],
                'quantity' => $data['quantity'],
                'item_type' => $data['item_type'] ?? null,
                'customer_picture' => $data['customer_picture'] ?? null,
                'created_at' => Carbon::now(),
            ]);
            $message = 'Item added to cart!';
        }

        return [
            'success' => true,
            'message' => $message,
            'cart_count' => cartMod::where('customer_id', Auth::guard('cust_login')->id())->count()
        ];
    }

    /**
     * Add item to guest cart
     *
     * @param array $data
     * @return array
     */
    private function addToGuestCart($data)
    {
        $cart = session()->get('guest_cart', []);
        $itemKey = $data['product_id'] . '_' . $data['color_id'] . '_' . $data['size_id'] . '_' . ($data['item_type'] ?? 'default');

        if (isset($cart[$itemKey])) {
            $cart[$itemKey]['quantity'] += $data['quantity'];
            $message = 'Item quantity updated in cart!';
        } else {
            $cart[$itemKey] = [
                'id' => uniqid(), // Temporary ID for guest cart
                'product_id' => $data['product_id'],
                'color_id' => $data['color_id'],
                'size_id' => $data['size_id'],
                'quantity' => $data['quantity'],
                'item_type' => $data['item_type'] ?? null,
                'customer_picture' => $data['customer_picture'] ?? null,
                'created_at' => now()->toDateTimeString(),
            ];
            $message = 'Item added to cart!';
        }

        session()->put('guest_cart', $cart);

        return [
            'success' => true,
            'message' => $message,
            'cart_count' => count($cart)
        ];
    }

    /**
     * Merge guest cart with user cart when user logs in
     *
     * @param int $userId
     * @return void
     */
    public function mergeGuestCartWithUser($userId)
    {
        $guestCart = session()->get('guest_cart', []);

        if (empty($guestCart)) {
            return;
        }

        foreach ($guestCart as $item) {
            $existingItem = cartMod::where('customer_id', $userId)
                ->where('product_id', $item['product_id'])
                ->where('color_id', $item['color_id'])
                ->where('size_id', $item['size_id'])
                ->where('item_type', $item['item_type'] ?? null)
                ->first();

            if ($existingItem) {
                // Update quantity if item already exists
                $existingItem->increment('quantity', $item['quantity']);
            } else {
                // Create new cart item
                cartMod::create([
                    'customer_id' => $userId,
                    'product_id' => $item['product_id'],
                    'color_id' => $item['color_id'],
                    'size_id' => $item['size_id'],
                    'quantity' => $item['quantity'],
                    'item_type' => $item['item_type'] ?? null,
                    'customer_picture' => $item['customer_picture'] ?? null,
                    'created_at' => Carbon::now(),
                ]);
            }
        }

        // Clear guest cart after merging
        session()->forget('guest_cart');
    }

    /**
     * Clear cart for current user
     *
     * @return void
     */
    public function clearCart()
    {
        if (Auth::guard('cust_login')->check()) {
            cartMod::where('customer_id', Auth::guard('cust_login')->id())->delete();
        } else {
            session()->forget('guest_cart');
        }
    }

    /**
     * Get cart count for current user
     *
     * @return int
     */
    public function getCartCount()
    {
        if (Auth::guard('cust_login')->check()) {
            return cartMod::where('customer_id', Auth::guard('cust_login')->id())->count();
        }

        return count(session()->get('guest_cart', []));
    }

    /**
     * Remove specific item from cart
     *
     * @param string $itemId
     * @return bool
     */
    public function removeFromCart($itemId)
    {
        if (Auth::guard('cust_login')->check()) {
            return cartMod::where('id', $itemId)
                ->where('customer_id', Auth::guard('cust_login')->id())
                ->delete();
        }

        $cart = session()->get('guest_cart', []);
        foreach ($cart as $key => $item) {
            if ($item['id'] === $itemId) {
                unset($cart[$key]);
                session()->put('guest_cart', $cart);
                return true;
            }
        }

        return false;
    }
}
