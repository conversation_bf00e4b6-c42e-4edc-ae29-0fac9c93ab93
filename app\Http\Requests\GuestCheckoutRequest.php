<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GuestCheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            // Personal Information
            'name' => [
                'required',
                'string',
                'min:2',
                'max:100',
                'regex:/^[a-zA-Z\s\-\'\.]+$/', // Only letters, spaces, hyphens, apostrophes, dots
            ],
            'email' => [
                'required',
                'email:rfc,dns',
                'max:255',
                'regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
            ],
            'company' => [
                'nullable',
                'string',
                'max:100',
                'regex:/^[a-zA-Z0-9\s\-\&\.\,]+$/', // Company name characters
            ],
            
            // Contact Information
            'mobile' => [
                'required',
                'string',
                'min:6',
                'max:15',
                'regex:/^[0-9\-\+\(\)\s]+$/', // Phone number characters
            ],
            'code' => [
                'required',
                'string',
                'max:10',
                'regex:/^\+?[0-9]+$/', // Country code format
            ],
            
            // Address Information
            'address' => [
                'required',
                'string',
                'min:5',
                'max:255',
                'regex:/^[a-zA-Z0-9\s\-\,\.\/\#]+$/', // Address characters
            ],
            'country' => [
                'required',
                'integer',
                'exists:countries,id',
            ],
            'city' => [
                'required',
                'integer',
                'exists:cities,id',
            ],
            'zip' => [
                'required',
                'string',
                'min:3',
                'max:10',
                'regex:/^[a-zA-Z0-9\-\s]+$/', // ZIP/Postal code format
            ],
            'note' => [
                'nullable',
                'string',
                'max:500',
                'regex:/^[a-zA-Z0-9\s\-\,\.\/\#\(\)\&]+$/', // Safe note characters
            ],
            
            // Order Information
            'delivery_charge' => [
                'required',
                'numeric',
                'min:0',
                'max:9999',
            ],
            'payment_method' => [
                'required',
                'integer',
                Rule::in([1, 2, 3]), // 1=COD, 2=SSL, 3=Stripe
            ],
            'subtotal' => [
                'required',
                'numeric',
                'min:0',
            ],
            'discount' => [
                'nullable',
                'numeric',
                'min:0',
            ],
            'ftotal' => [
                'required',
                'numeric',
                'min:0',
            ],
            
            // Guest Registration (optional)
            'create_account' => [
                'nullable',
                'boolean',
            ],
            'password' => [
                'required_if:create_account,true',
                'nullable',
                'string',
                'min:8',
                'max:255',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/', // Strong password
            ],
            'password_confirmation' => [
                'required_if:create_account,true',
                'nullable',
                'same:password',
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.regex' => 'Name can only contain letters, spaces, hyphens, apostrophes, and dots.',
            'email.email' => 'Please enter a valid email address.',
            'email.regex' => 'Email format is invalid.',
            'mobile.regex' => 'Phone number contains invalid characters.',
            'code.regex' => 'Country code must contain only numbers and optional plus sign.',
            'address.regex' => 'Address contains invalid characters.',
            'zip.regex' => 'ZIP/Postal code format is invalid.',
            'note.regex' => 'Additional information contains invalid characters.',
            'payment_method.in' => 'Please select a valid payment method.',
            'password.regex' => 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.',
            'password_confirmation.same' => 'Password confirmation does not match.',
            'country.exists' => 'Please select a valid country.',
            'city.exists' => 'Please select a valid city.',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Sanitize inputs
        $this->merge([
            'name' => $this->sanitizeString($this->name),
            'email' => $this->sanitizeEmail($this->email),
            'company' => $this->sanitizeString($this->company),
            'mobile' => $this->sanitizeString($this->mobile),
            'address' => $this->sanitizeString($this->address),
            'note' => $this->sanitizeString($this->note),
        ]);
    }

    /**
     * Sanitize string input
     *
     * @param string|null $value
     * @return string|null
     */
    private function sanitizeString($value)
    {
        if (is_null($value)) {
            return null;
        }
        
        return trim(strip_tags($value));
    }

    /**
     * Sanitize email input
     *
     * @param string|null $value
     * @return string|null
     */
    private function sanitizeEmail($value)
    {
        if (is_null($value)) {
            return null;
        }
        
        return trim(strtolower(strip_tags($value)));
    }
}
