<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Services\CartService;
use App\Models\cartMod;
use App\Models\Product_list;
use App\Models\Color;
use App\Models\Size;
use App\Models\CustInfo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CartServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $cartService;
    protected $product;
    protected $color;
    protected $size;
    protected $customer;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->cartService = new CartService();
        
        // Create test data
        $this->product = Product_list::create([
            'product_name' => 'Test Product',
            'regular_price' => 100,
            'after_disc' => 90,
            'subcata_id' => 1
        ]);

        $this->color = Color::create(['color_name' => 'Red']);
        $this->size = Size::create(['size_name' => 'M']);
        
        $this->customer = CustInfo::create([
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now()
        ]);
    }

    /** @test */
    public function it_can_add_item_to_guest_cart()
    {
        $result = $this->cartService->addToCart([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'quantity' => 2
        ]);

        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['cart_count']);
        $this->assertStringContainsString('added', $result['message']);
    }

    /** @test */
    public function it_can_add_item_to_authenticated_user_cart()
    {
        Auth::guard('cust_login')->login($this->customer);

        $result = $this->cartService->addToCart([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'quantity' => 2
        ]);

        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['cart_count']);
        
        $this->assertDatabaseHas('cart_mods', [
            'customer_id' => $this->customer->id,
            'product_id' => $this->product->id,
            'quantity' => 2
        ]);
    }

    /** @test */
    public function it_updates_quantity_for_existing_cart_items()
    {
        // Add item first time
        $this->cartService->addToCart([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'quantity' => 1
        ]);

        // Add same item again
        $result = $this->cartService->addToCart([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'quantity' => 2
        ]);

        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['cart_count']); // Still 1 unique item
        $this->assertStringContainsString('updated', $result['message']);

        // Check quantity is updated in session
        $guestCart = session('guest_cart', []);
        $cartItem = array_values($guestCart)[0];
        $this->assertEquals(3, $cartItem['quantity']); // 1 + 2 = 3
    }

    /** @test */
    public function it_can_get_guest_cart_items()
    {
        // Add item to guest cart
        session(['guest_cart' => [
            'test_key' => [
                'id' => 'test_id',
                'product_id' => $this->product->id,
                'color_id' => $this->color->id,
                'size_id' => $this->size->id,
                'quantity' => 2
            ]
        ]]);

        $cartItems = $this->cartService->getGuestCartItems();
        
        $this->assertCount(1, $cartItems);
        $this->assertEquals(2, $cartItems->first()->quantity);
        $this->assertEquals($this->product->id, $cartItems->first()->product_id);
    }

    /** @test */
    public function it_can_merge_guest_cart_with_user_cart()
    {
        // Add items to guest cart
        session(['guest_cart' => [
            'item1' => [
                'product_id' => $this->product->id,
                'color_id' => $this->color->id,
                'size_id' => $this->size->id,
                'quantity' => 2,
                'item_type' => null,
                'customer_picture' => null
            ]
        ]]);

        // Merge with user cart
        $this->cartService->mergeGuestCartWithUser($this->customer->id);

        // Check that item is now in database
        $this->assertDatabaseHas('cart_mods', [
            'customer_id' => $this->customer->id,
            'product_id' => $this->product->id,
            'quantity' => 2
        ]);

        // Check that guest cart is cleared
        $this->assertEmpty(session('guest_cart', []));
    }

    /** @test */
    public function it_merges_quantities_for_existing_items_during_merge()
    {
        // Add item to user's database cart
        cartMod::create([
            'customer_id' => $this->customer->id,
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'quantity' => 1
        ]);

        // Add same item to guest cart
        session(['guest_cart' => [
            'item1' => [
                'product_id' => $this->product->id,
                'color_id' => $this->color->id,
                'size_id' => $this->size->id,
                'quantity' => 2,
                'item_type' => null,
                'customer_picture' => null
            ]
        ]]);

        // Merge carts
        $this->cartService->mergeGuestCartWithUser($this->customer->id);

        // Check that quantities are merged (1 + 2 = 3)
        $cartItem = cartMod::where('customer_id', $this->customer->id)->first();
        $this->assertEquals(3, $cartItem->quantity);
    }

    /** @test */
    public function it_can_clear_cart()
    {
        // Add item to guest cart
        session(['guest_cart' => ['item1' => ['test' => 'data']]]);
        
        $this->cartService->clearCart();
        
        $this->assertEmpty(session('guest_cart', []));
    }

    /** @test */
    public function it_can_get_cart_count()
    {
        // Add items to guest cart
        session(['guest_cart' => [
            'item1' => ['test' => 'data1'],
            'item2' => ['test' => 'data2']
        ]]);

        $count = $this->cartService->getCartCount();
        
        $this->assertEquals(2, $count);
    }

    /** @test */
    public function it_can_remove_item_from_guest_cart()
    {
        // Add items to guest cart
        session(['guest_cart' => [
            'item1' => ['id' => 'test_id_1', 'test' => 'data1'],
            'item2' => ['id' => 'test_id_2', 'test' => 'data2']
        ]]);

        $result = $this->cartService->removeFromCart('test_id_1');
        
        $this->assertTrue($result);
        
        $guestCart = session('guest_cart', []);
        $this->assertCount(1, $guestCart);
        
        // Check that the correct item was removed
        $remainingItem = array_values($guestCart)[0];
        $this->assertEquals('test_id_2', $remainingItem['id']);
    }

    /** @test */
    public function it_handles_empty_guest_cart_gracefully()
    {
        $cartItems = $this->cartService->getGuestCartItems();
        $this->assertCount(0, $cartItems);

        $count = $this->cartService->getCartCount();
        $this->assertEquals(0, $count);

        // Merge should not fail with empty cart
        $this->cartService->mergeGuestCartWithUser($this->customer->id);
        $this->assertTrue(true); // No exception thrown
    }
}
