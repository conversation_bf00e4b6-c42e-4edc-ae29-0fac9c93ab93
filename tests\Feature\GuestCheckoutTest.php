<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\CustInfo;
use App\Models\Product_list;
use App\Models\Color;
use App\Models\Size;
use App\Models\Country;
use App\Models\City;
use App\Models\OrderTab;
use App\Models\BillingTab;
use App\Models\OrdereditemsTab;
use App\Services\CartService;
use App\Services\GuestRegistrationService;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;

class GuestCheckoutTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create test country and city
        $this->country = Country::create([
            'name' => 'Finland',
            'phonecode' => '+358'
        ]);

        $this->city = City::create([
            'name' => 'Helsinki',
            'country_id' => $this->country->id
        ]);

        // Create test product
        $this->product = Product_list::create([
            'product_name' => 'Test Product',
            'regular_price' => 100,
            'after_disc' => 90,
            'subcata_id' => 1,
            'product_details' => 'Test product details'
        ]);

        // Create test color and size
        $this->color = Color::create(['color_name' => 'Red']);
        $this->size = Size::create(['size_name' => 'M']);
    }

    /** @test */
    public function guest_can_add_items_to_cart()
    {
        $cartService = new CartService();
        
        $result = $cartService->addToCart([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'quantity' => 2
        ]);

        $this->assertTrue($result['success']);
        $this->assertEquals(1, $result['cart_count']);
        
        // Check session cart
        $guestCart = session('guest_cart', []);
        $this->assertCount(1, $guestCart);
    }

    /** @test */
    public function guest_can_view_checkout_page()
    {
        // Add item to guest cart
        session(['guest_cart' => [
            'test_item' => [
                'id' => 'test_id',
                'product_id' => $this->product->id,
                'color_id' => $this->color->id,
                'size_id' => $this->size->id,
                'quantity' => 1
            ]
        ]]);

        $response = $this->get(route('checkout'));
        
        $response->assertStatus(200);
        $response->assertViewIs('frontend.checkout');
    }

    /** @test */
    public function guest_checkout_requires_valid_data()
    {
        // Add item to guest cart
        session(['guest_cart' => [
            'test_item' => [
                'id' => 'test_id',
                'product_id' => $this->product->id,
                'color_id' => $this->color->id,
                'size_id' => $this->size->id,
                'quantity' => 1
            ]
        ]]);

        $response = $this->post(route('billing.store'), []);
        
        $response->assertSessionHasErrors([
            'name', 'email', 'mobile', 'code', 'address', 
            'country', 'city', 'zip', 'delivery_charge', 'payment_method'
        ]);
    }

    /** @test */
    public function guest_can_complete_checkout_with_valid_data()
    {
        // Add item to guest cart
        session(['guest_cart' => [
            'test_item' => [
                'id' => 'test_id',
                'product_id' => $this->product->id,
                'color_id' => $this->color->id,
                'size_id' => $this->size->id,
                'quantity' => 1
            ]
        ]]);

        // Set session totals
        session([
            'total' => 90,
            'discount' => 0,
            'ftotal' => 90
        ]);

        $checkoutData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'mobile' => '**********',
            'code' => '+358',
            'address' => '123 Test Street',
            'country' => $this->country->id,
            'city' => $this->city->id,
            'zip' => '00100',
            'delivery_charge' => 10,
            'payment_method' => 1, // COD
            'subtotal' => 90,
            'discount' => 0,
            'ftotal' => 90
        ];

        $response = $this->post(route('billing.store'), $checkoutData);
        
        $response->assertRedirect(route('order.complete'));
        $this->assertNotNull(session('order_id'));
    }

    /** @test */
    public function guest_order_is_created_with_null_customer_id()
    {
        // Add item to guest cart and complete checkout
        session(['guest_cart' => [
            'test_item' => [
                'id' => 'test_id',
                'product_id' => $this->product->id,
                'color_id' => $this->color->id,
                'size_id' => $this->size->id,
                'quantity' => 1
            ]
        ]]);

        session([
            'total' => 90,
            'discount' => 0,
            'ftotal' => 90
        ]);

        $checkoutData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'mobile' => '**********',
            'code' => '+358',
            'address' => '123 Test Street',
            'country' => $this->country->id,
            'city' => $this->city->id,
            'zip' => '00100',
            'delivery_charge' => 10,
            'payment_method' => 1,
            'subtotal' => 90,
            'discount' => 0,
            'ftotal' => 90
        ];

        $this->post(route('billing.store'), $checkoutData);
        $this->get(route('order.complete'));

        // Check that order was created with null customer_id
        $order = OrderTab::latest()->first();
        $this->assertNull($order->customer_id);
        
        // Check billing record
        $billing = BillingTab::latest()->first();
        $this->assertNull($billing->customer_id);
        $this->assertEquals('<EMAIL>', $billing->email);
    }

    /** @test */
    public function guest_can_create_account_during_checkout()
    {
        session(['guest_cart' => [
            'test_item' => [
                'id' => 'test_id',
                'product_id' => $this->product->id,
                'color_id' => $this->color->id,
                'size_id' => $this->size->id,
                'quantity' => 1
            ]
        ]]);

        session([
            'total' => 90,
            'discount' => 0,
            'ftotal' => 90
        ]);

        $checkoutData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'mobile' => '**********',
            'code' => '+358',
            'address' => '123 Test Street',
            'country' => $this->country->id,
            'city' => $this->city->id,
            'zip' => '00100',
            'delivery_charge' => 10,
            'payment_method' => 1,
            'subtotal' => 90,
            'discount' => 0,
            'ftotal' => 90,
            'create_account' => true,
            'password' => 'SecurePass123!',
            'password_confirmation' => 'SecurePass123!'
        ];

        $this->post(route('billing.store'), $checkoutData);
        $this->get(route('order.complete'));

        // Check that customer account was created
        $customer = CustInfo::where('email', '<EMAIL>')->first();
        $this->assertNotNull($customer);
        
        // Check that order is linked to customer
        $order = OrderTab::latest()->first();
        $this->assertEquals($customer->id, $order->customer_id);
    }

    /** @test */
    public function cart_merge_works_when_guest_logs_in()
    {
        // Create a customer
        $customer = CustInfo::create([
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'email_verified_at' => now()
        ]);

        // Add items to guest cart
        $cartService = new CartService();
        $cartService->addToCart([
            'product_id' => $this->product->id,
            'color_id' => $this->color->id,
            'size_id' => $this->size->id,
            'quantity' => 2
        ]);

        // Login the customer
        Auth::guard('cust_login')->login($customer);
        
        // Merge cart
        $cartService->mergeGuestCartWithUser($customer->id);

        // Check that cart items are now in database
        $this->assertDatabaseHas('cart_mods', [
            'customer_id' => $customer->id,
            'product_id' => $this->product->id,
            'quantity' => 2
        ]);

        // Check that guest cart is cleared
        $this->assertEmpty(session('guest_cart', []));
    }

    /** @test */
    public function checkout_validates_malicious_input()
    {
        session(['guest_cart' => [
            'test_item' => [
                'id' => 'test_id',
                'product_id' => $this->product->id,
                'color_id' => $this->color->id,
                'size_id' => $this->size->id,
                'quantity' => 1
            ]
        ]]);

        $maliciousData = [
            'name' => '<script>alert("xss")</script>',
            'email' => 'not-an-email',
            'mobile' => 'abc123',
            'address' => '<img src=x onerror=alert(1)>',
            'note' => '<script>document.location="http://evil.com"</script>',
            'country' => 999999,
            'city' => 999999,
            'zip' => '<script>',
            'delivery_charge' => -10,
            'payment_method' => 999
        ];

        $response = $this->post(route('billing.store'), $maliciousData);
        
        $response->assertSessionHasErrors();
    }
}
