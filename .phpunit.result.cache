{"version": 1, "defects": {"Tests\\Feature\\GuestCheckoutTest::guest_can_add_items_to_cart": 4, "Tests\\Feature\\GuestCheckoutTest::guest_can_view_checkout_page": 4, "Tests\\Feature\\GuestCheckoutTest::guest_checkout_requires_valid_data": 4, "Tests\\Feature\\GuestCheckoutTest::guest_can_complete_checkout_with_valid_data": 4, "Tests\\Feature\\GuestCheckoutTest::guest_order_is_created_with_null_customer_id": 4, "Tests\\Feature\\GuestCheckoutTest::guest_can_create_account_during_checkout": 4, "Tests\\Feature\\GuestCheckoutTest::cart_merge_works_when_guest_logs_in": 4, "Tests\\Feature\\GuestCheckoutTest::checkout_validates_malicious_input": 4}, "times": {"Tests\\Feature\\GuestCheckoutTest::guest_can_add_items_to_cart": 4.436, "Tests\\Feature\\GuestCheckoutTest::guest_can_view_checkout_page": 4.093, "Tests\\Feature\\GuestCheckoutTest::guest_checkout_requires_valid_data": 4.125, "Tests\\Feature\\GuestCheckoutTest::guest_can_complete_checkout_with_valid_data": 4.136, "Tests\\Feature\\GuestCheckoutTest::guest_order_is_created_with_null_customer_id": 4.127, "Tests\\Feature\\GuestCheckoutTest::guest_can_create_account_during_checkout": 4.114, "Tests\\Feature\\GuestCheckoutTest::cart_merge_works_when_guest_logs_in": 4.124, "Tests\\Feature\\GuestCheckoutTest::checkout_validates_malicious_input": 4.11}}