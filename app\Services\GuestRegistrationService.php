<?php

namespace App\Services;

use App\Models\CustInfo;
use App\Models\OrderTab;
use App\Models\OrdereditemsTab;
use App\Models\BillingTab;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class GuestRegistrationService
{
    /**
     * Create account for guest user and link existing order
     *
     * @param array $data
     * @param string $orderId
     * @return CustInfo|null
     */
    public function createAccountFromGuestOrder($data, $orderId)
    {
        try {
            DB::beginTransaction();

            // Check if email already exists
            $existingUser = CustInfo::where('email', $data['email'])->first();
            if ($existingUser) {
                throw new \Exception('An account with this email already exists.');
            }

            // Create new customer account
            $customer = CustInfo::create([
                'name' => $data['name'],
                'email' => $data['email'],
                'password' => Hash::make($data['password']),
                'mobile' => $data['code'] . $data['mobile'],
                'address' => $data['address'],
                'city' => $data['city'],
                'country' => $data['country'],
                'status' => 1,
                'email_verified_at' => Carbon::now(), // Auto-verify for checkout users
                'created_at' => Carbon::now(),
            ]);

            // Link the order to the new customer
            $this->linkOrderToCustomer($orderId, $customer->id);

            DB::commit();

            return $customer;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Link guest order to newly created customer account
     *
     * @param string $orderId
     * @param int $customerId
     * @return void
     */
    private function linkOrderToCustomer($orderId, $customerId)
    {
        // Update order table
        OrderTab::where('order_id', $orderId)
            ->whereNull('customer_id')
            ->update(['customer_id' => $customerId]);

        // Update ordered items table
        OrdereditemsTab::where('order_id', $orderId)
            ->whereNull('customer_id')
            ->update(['customer_id' => $customerId]);

        // Update billing table
        BillingTab::where('order_id', $orderId)
            ->whereNull('customer_id')
            ->update(['customer_id' => $customerId]);
    }

    /**
     * Check if email is available for registration
     *
     * @param string $email
     * @return bool
     */
    public function isEmailAvailable($email)
    {
        return !CustInfo::where('email', $email)->exists();
    }

    /**
     * Validate password strength
     *
     * @param string $password
     * @return array
     */
    public function validatePasswordStrength($password)
    {
        $errors = [];

        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long.';
        }

        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter.';
        }

        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter.';
        }

        if (!preg_match('/\d/', $password)) {
            $errors[] = 'Password must contain at least one number.';
        }

        if (!preg_match('/[@$!%*?&]/', $password)) {
            $errors[] = 'Password must contain at least one special character (@$!%*?&).';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Generate secure random password
     *
     * @param int $length
     * @return string
     */
    public function generateSecurePassword($length = 12)
    {
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $numbers = '0123456789';
        $special = '@$!%*?&';

        $password = '';
        
        // Ensure at least one character from each category
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $special[random_int(0, strlen($special) - 1)];

        // Fill the rest randomly
        $allChars = $lowercase . $uppercase . $numbers . $special;
        for ($i = 4; $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        // Shuffle the password
        return str_shuffle($password);
    }
}
